/* Global CSS variables for Thinkertags Tools */
:root {
  --primary-color: #121212;
  --secondary-color: #333333;
  --accent-color: #0066cc;
  --accent-highlight: #0f4e8d;
  --light-color: #f8f8f8;
  --text-color: #060606;
  --light-text: #ffffff;
  --border-radius: 8px;
  --border-radius-cta: 30px;
  --transition-speed: 0.3s;
  --container-width: 1200px;
  --spacing-xs: 8px;
  --spacing-sm: 16px;
  --spacing-md: 24px;
  --spacing-lg: 48px;
  --spacing-xl: 80px;
}

/* Font face for The Bold Font */
@font-face {
  font-family: 'The Bold Font';
  src: 
  url('/fonts/THEBOLDFONT.ttf') format('truetype'),
  url('/fonts/THEBOLDFONT.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
}

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #ffffff;
  color: var(--text-color);
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Navigation */
.nav {
  background-color: var(--primary-color);
  color: var(--light-text);
  padding: 1rem 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nav-container {
  max-width: var(--container-width);
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--light-text);
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: var(--light-text);
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  transition: background-color var(--transition-speed);
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Main content */
.main-content {
  flex: 1;
  padding: 2rem;
}

/* Home page */
.home {
  max-width: var(--container-width);
  margin: 0 auto;
  text-align: center;
}

.home h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
}

.home p {
  font-size: 1.2rem;
  margin-bottom: 3rem;
  color: var(--secondary-color);
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.tool-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: var(--border-radius);
  padding: 2rem;
  text-align: left;
  transition: box-shadow var(--transition-speed);
}

.tool-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.tool-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
}

.tool-card p {
  color: var(--secondary-color);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.tool-link {
  color: var(--accent-color);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--transition-speed);
}

.tool-link:hover {
  color: var(--accent-highlight);
}

/* Responsive design */
@media (max-width: 768px) {
  .nav-container {
    flex-direction: column;
    gap: 1rem;
  }
  
  .nav-links {
    gap: 1rem;
  }
  
  .main-content {
    padding: 1rem;
  }
  
  .home h1 {
    font-size: 2rem;
  }
  
  .tools-grid {
    grid-template-columns: 1fr;
  }
}
