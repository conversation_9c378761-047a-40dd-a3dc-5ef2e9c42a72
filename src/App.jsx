import React from 'react'
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom'
import FontConverter from './components/FontConverter'
import LogoView from './components/LogoView'
import './styles/App.css'

function App() {
  return (
    <Router>
      <div className="app">
        <nav className="nav">
          <div className="nav-container">
            <h1 className="nav-title">Thinkertags Tools</h1>
            <div className="nav-links">
              <Link to="/" className="nav-link">Home</Link>
              <Link to="/font-converter" className="nav-link">Font Converter</Link>
              <Link to="/logo-view" className="nav-link">Logo View</Link>
            </div>
          </div>
        </nav>

        <main className="main-content">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/font-converter" element={<FontConverter />} />
            <Route path="/logo-view" element={<LogoView />} />
          </Routes>
        </main>
      </div>
    </Router>
  )
}

function Home() {
  return (
    <div className="home">
      <h1>Thinkertags Development Tools</h1>
      <p>A collection of development and debugging tools for the Thinkertags project.</p>
      
      <div className="tools-grid">
        <div className="tool-card">
          <h3>Font Converter</h3>
          <p>Convert base64 encoded WOFF fonts to downloadable files.</p>
          <Link to="/font-converter" className="tool-link">Open Tool →</Link>
        </div>
        
        <div className="tool-card">
          <h3>Logo View</h3>
          <p>Interactive logo transformation tool with SVG export capabilities.</p>
          <Link to="/logo-view" className="tool-link">Open Tool →</Link>
        </div>
      </div>
    </div>
  )
}

export default App
