import React, { useState, useEffect } from 'react';
import '../styles/FontConverter.css';

const FontConverter = () => {
  const [base64Input, setBase64Input] = useState('');
  const [woffFont, setWoffFont] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState('');
  const [fileName, setFileName] = useState('font');

  // Clean up object URLs when component unmounts
  useEffect(() => {
    return () => {
      if (woffFont && woffFont.url) {
        URL.revokeObjectURL(woffFont.url);
      }
    };
  }, [woffFont]);

  const handleBase64InputChange = (e) => {
    setBase64Input(e.target.value);
    setError('');
    setWoffFont(null);
  };

  const handleFileNameChange = (e) => {
    setFileName(e.target.value || 'font');
  };

  const processFont = () => {
    if (!base64Input.trim()) {
      setError('Please paste a base64 encoded WOFF font string');
      return;
    }

    setIsProcessing(true);
    setError('');

    try {
      // Clean up the base64 string (remove any data URL prefix if present)
      let cleanBase64 = base64Input.trim();
      if (cleanBase64.includes(',')) {
        cleanBase64 = cleanBase64.split(',')[1];
      }

      // Remove any whitespace, newlines, or other non-base64 characters
      cleanBase64 = cleanBase64.replace(/[^A-Za-z0-9+/=]/g, '');

      // Validate base64 string
      if (cleanBase64.length % 4 !== 0) {
        throw new Error('Invalid base64 string (length should be multiple of 4)');
      }

      // Convert base64 to binary data
      const binaryString = atob(cleanBase64);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      // Check if it looks like a WOFF file (WOFF files start with 'wOFF' signature)
      const signature = String.fromCharCode(bytes[0], bytes[1], bytes[2], bytes[3]);
      if (signature !== 'wOFF') {
        console.warn('Warning: File signature does not match WOFF format:', signature);
        // Continue anyway, as the user specifically requested this functionality
      }

      // Create a blob with WOFF mime type
      const fontBlob = new Blob([bytes], { type: 'font/woff' });

      // Create a download URL
      const downloadUrl = URL.createObjectURL(fontBlob);

      setWoffFont({
        url: downloadUrl,
        filename: `${fileName}.woff`
      });
    } catch (err) {
      console.error('Processing error:', err);
      setError('Error processing font: ' + err.message);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDownload = () => {
    if (!woffFont) return;

    const a = document.createElement('a');
    a.href = woffFont.url;
    a.download = woffFont.filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  return (
    <div className="font-converter-container">
      <h1>WOFF Font Downloader</h1>

      <div className="input-section">
        <div className="base64-input-container">
          <label htmlFor="base64Input">Paste WOFF font as base64 string:</label>
          <textarea
            id="base64Input"
            className="base64-input"
            value={base64Input}
            onChange={handleBase64InputChange}
            placeholder="Paste your base64 encoded WOFF font here..."
            rows={6}
          />
        </div>

        <div className="filename-input-container">
          <label htmlFor="fileNameInput">Output filename (without extension):</label>
          <input
            id="fileNameInput"
            type="text"
            className="filename-input"
            value={fileName}
            onChange={handleFileNameChange}
            placeholder="font"
          />
        </div>
      </div>

      {error && <div className="error-message">{error}</div>}

      <div className="action-buttons">
        <button
          className="convert-button"
          onClick={processFont}
          disabled={!base64Input.trim() || isProcessing}
        >
          {isProcessing ? 'Processing...' : 'Process WOFF Font'}
        </button>

        {woffFont && (
          <button
            className="download-button"
            onClick={handleDownload}
          >
            Download WOFF
          </button>
        )}
      </div>

      <div className="note">
        <p>This tool allows you to download a WOFF font from a base64 encoded string.</p>
        <p>Paste your base64 encoded WOFF font string in the text area above. The processing happens entirely in your browser - no files are uploaded to any server.</p>
        <p><strong>Note:</strong> The tool will automatically clean up the base64 string by:</p>
        <ul>
          <li>Removing any data URL prefix (like "data:font/woff;base64,")</li>
          <li>Removing whitespace, newlines, and other non-base64 characters</li>
          <li>Validating that the string is properly formatted</li>
        </ul>
        <p>The tool will also check if the file appears to be a valid WOFF font by looking for the 'wOFF' signature.</p>
      </div>
    </div>
  );
};

export default FontConverter;
