{"name": "thinkertags-tools", "private": true, "version": "1.0.0", "type": "module", "description": "Development tools and utilities for Thinkertags", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"opentype.js": "^1.3.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.2"}, "devDependencies": {"@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@vitejs/plugin-react": "^4.4.1", "vite": "^6.3.5"}}