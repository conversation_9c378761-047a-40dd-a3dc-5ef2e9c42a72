# Thinkertags Tools

A collection of development and debugging tools for the Thinkertags project.

## Overview

This repository contains standalone tools that were previously part of the main Thinkertags codebase. These tools are designed for development, debugging, and content creation purposes.

## Tools Included

### 1. Font Converter
- **Path**: `/font-converter`
- **Purpose**: Convert base64 encoded WOFF fonts to downloadable files
- **Features**:
  - Paste base64 encoded WOFF font strings
  - Automatic cleanup of base64 strings (removes data URL prefixes, whitespace, etc.)
  - Validation of base64 format and WOFF file signatures
  - Download processed fonts as `.woff` files
  - Custom filename support

### 2. Logo View
- **Path**: `/logo-view`
- **Purpose**: Interactive logo transformation tool with SVG export capabilities
- **Features**:
  - Real-time logo transformations (scale, rotate, skew, etc.)
  - Advanced effects (bounce, arc curve)
  - CSS property copying
  - SVG export with path conversion using opentype.js
  - Responsive controls interface
  - Font loading with fallback support

## Getting Started

### Prerequisites
- Node.js (version 16 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd thinkertags-tools
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:3001`

## Available Scripts

- `npm run dev` - Start the development server
- `npm run build` - Build the project for production
- `npm run preview` - Preview the production build

## Project Structure

```
thinkertags-tools/
├── src/
│   ├── components/
│   │   ├── FontConverter.jsx
│   │   └── LogoView.jsx
│   ├── styles/
│   │   ├── App.css
│   │   ├── FontConverter.css
│   │   └── LogoView.css
│   ├── App.jsx
│   └── main.jsx
├── public/
├── index.html
├── package.json
├── vite.config.js
└── README.md
```

## Dependencies

### Core Dependencies
- **React 18** - UI framework
- **React Router DOM** - Client-side routing
- **opentype.js** - Font parsing and path generation for SVG export

### Development Dependencies
- **Vite** - Build tool and development server
- **@vitejs/plugin-react** - React plugin for Vite

## Usage

### Font Converter
1. Navigate to `/font-converter`
2. Paste your base64 encoded WOFF font string
3. Optionally customize the output filename
4. Click "Process WOFF Font"
5. Download the generated font file

### Logo View
1. Navigate to `/logo-view`
2. Use the control sliders to adjust logo properties
3. Toggle controls visibility with the button in the top-right
4. Copy CSS properties or export as SVG
5. The tool automatically attempts to load custom fonts for better SVG export

## Technical Notes

- The Logo View tool runs on port 3001 by default to avoid conflicts with the main Thinkertags project
- Font loading attempts multiple paths and falls back to system fonts if custom fonts aren't available
- SVG export uses opentype.js when available for true path conversion, otherwise falls back to text elements
- All processing happens client-side - no files are uploaded to servers

## Contributing

This is a utility repository for the Thinkertags project. When adding new tools:

1. Create components in `src/components/`
2. Add corresponding CSS files in `src/styles/`
3. Update the routing in `App.jsx`
4. Add tool cards to the home page
5. Update this README

## License

This project is part of the Thinkertags ecosystem.
